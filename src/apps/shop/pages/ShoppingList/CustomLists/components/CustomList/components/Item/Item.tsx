import { useState } from 'react';
import { ProductType } from '@/types';
import { Actions } from '../Actions/Actions';
import { Badge } from '@/libs/ui/Badge/Badge';
import { Dollar } from '@/libs/products/components/Dollar/Dollar';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Link } from 'react-router-dom';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';

type Props = {
  product: ProductType;
  isHighlighted?: boolean;
};

export const Item = ({ product, isHighlighted = false }: Props) => {
  const [currentOffer, setCurrentOffer] = useState(product.offers[0]);
  const productUrl = getProductUrl(product.id, currentOffer.id);

  const handleSwapVendor = (newOfferId: string) => {
    setCurrentOffer(product.offers.find(({ id }) => newOfferId === id)!);
  };

  const { salePrice, originalPrice } =
    getProductOfferComputedData(currentOffer);

  // Dynamic classes for highlighted items
  const containerClasses = isHighlighted
    ? 'flex w-full justify-between rounded-lg border-2 border-blue-500 bg-gradient-to-r from-blue-50 to-blue-100 p-4 pr-2 shadow-md'
    : 'flex w-full justify-between rounded-lg border border-black/[0.06] bg-white p-4 pr-2';

  return (
    <div className={containerClasses}>
      <div className="flex flex-col justify-center gap-1">
        <div className="mr-auto flex items-center gap-2">
          <Dollar toolTipLabel="Promotion Type: Bogo" size="0.7rem" />
          <Badge className="h-4 bg-[#ED1F22] text-[10px] text-white">
            Surgery Room
          </Badge>
        </div>
        <Link to={productUrl} className="mr-4 max-w-md text-sm font-semibold">
          {product.name}
        </Link>
        <div className="mr-auto flex">
          <span className="text-xs font-semibold">
            {currentOffer.vendor.name}
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            SKU: <span className="text-black">{currentOffer.vendorSku}</span>
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            Per unit:{' '}
            {originalPrice > salePrice ? (
              <>
                <span className="mx-1 text-xs font-semibold text-black">
                  ${salePrice}
                </span>
                <span className="text-sxs line-through">${originalPrice}</span>
              </>
            ) : (
              <span className="mx-1 text-xs font-semibold text-black">
                ${salePrice}
              </span>
            )}{' '}
          </span>
        </div>
      </div>
      <Actions
        product={product}
        originalPrice={originalPrice}
        salePrice={salePrice}
        currentOffer={currentOffer}
        onSwap={handleSwapVendor}
      />
    </div>
  );
};
