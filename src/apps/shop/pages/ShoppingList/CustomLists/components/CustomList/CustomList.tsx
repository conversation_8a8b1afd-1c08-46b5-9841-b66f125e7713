import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { EmptyState } from './components/EmptyState/EmptyState';
import { ProductType } from '@/types';
import { Item } from './components/Item/Item';
import { useProductSorting } from './hooks/useProductSorting';

type Props = {
  setActiveTab: (tab: number) => void;
  searchQuery: string;
  listName: string;
  products: ProductType[];
};

export const CustomList = ({
  setActiveTab,
  searchQuery,
  listName,
  products,
}: Props) => {
  const { sortedProducts, isProductMatch } = useProductSorting({
    products,
    searchQuery,
    listName,
  });

  return (
    <CollapsiblePanel
      variant="white"
      header={
        <div className="flex h-[72px] w-full items-center justify-between bg-white p-5 pr-16 pb-0">
          <Checkbox checked={false} onChange={() => {}} />
          <div className="ml-4 flex w-full flex-col gap-1">
            <span className="text-sm font-semibold">{listName}</span>
            <div className="flex">
              <span className="text-xs text-black/50">
                {sortedProducts.length} items
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Created by:{' '}
                <span className="font-semibold text-black/70">Lima Neto</span>
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Last updated: <span className="font-semibold">Sep 10</span>
              </span>
            </div>
          </div>
        </div>
      }
      content={
        <div className="flex flex-col gap-4 p-5 pt-0">
          <div className="flex w-full justify-end">
            <Button className="w-52" size="sm" onClick={() => setActiveTab(1)}>
              + Add products
            </Button>
          </div>
          <div className="flex flex-col gap-2">
            {sortedProducts && sortedProducts.length > 0 ? (
              sortedProducts.map((product) => (
                <Item
                  key={product.id}
                  product={product}
                  isHighlighted={isProductMatch(product)}
                />
              ))
            ) : (
              <EmptyState setActiveTab={setActiveTab} />
            )}
          </div>
          {!sortedProducts ||
            (sortedProducts.length === 0 && (
              <Button
                onClick={() => setActiveTab(1)}
                variant="unstyled"
                className="mt-4 w-full text-right text-blue-600"
              >
                <p className="mr-4 ml-auto">+ Add more products</p>
              </Button>
            ))}
        </div>
      }
      startOpen
    />
  );
};
