import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { EmptyState } from './components/EmptyState/EmptyState';
import { ProductType } from '@/types';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Item } from './components/Item/Item';

const productsData: ProductType[] = [
  {
    id: '9ed26eac-7240-4020-9366-fe04290b77c3',
    name: 'Simparica 6 x 5mg Gold 2.8 - 5.5lbs',
    imageUrl:
      'https://shop.zoetis.com/zb2b/medias/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjQ3NDN8aW1hZ2UvanBlZ3xhREZrTDJneVlpOHhNalUxTmpRMk1EWTROek01TUM4ek1EQlhlRE13TUVoZmJuVnNiQXwyNjJkZDg5OWQ1ZDZhN2NmMTUzMzU0NzNmNDEzZTQ5ODk0MjFhYjYyZmY4ZWUyYzA2NTIwY2JlNTQ2MTc1YzU1',
    isFavorite: false,
    manufacturer: null,
    manufacturerSku: '10012457',
    description:
      'Persistent Protection from ticks and fleas.nSimparicau00aeu00a0(sarolaner)u00a0is FDA-approved to block infections that may cause Lyme disease as a result of killingu00a0ixodes scapularis.',
    offers: [
      {
        id: '9d7581c7-ebe0-468e-8b8a-6221b3476f70',
        vendor: {
          id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
          name: 'Zoetis',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
          type: 'manufacturer' as const,
        },
        name: 'Simparica 6 x 5mg Gold 2.8 - 5.5lbs',
        isPurchasable: true,
        vendorSku: '10012457',
        price: '75.35',
        clinicPrice: null,
        stockStatus: 'IN_STOCK' as const,
        lastOrderedAt: '2025-09-09T16:43:13.000000Z',
        lastOrderedQuantity: 2,
        increments: 1,
        isRecommended: true,
        rebatePercent: '10.0',
        product: null as unknown as ProductType, // Circular reference - will be set after product creation
        unitOfMeasure: null,
        size: null,
      },
    ],
    attributes: [],
    isHazardous: false,
    requiresPrescription: false,
    requiresColdShipping: false,
    isControlledSubstance: false,
    isControlled222Form: false,
    requiresPedigree: false,
  },
  {
    id: '9ed2632c-c749-4bcf-8299-dc8cadd25363',
    name: 'Duramorph PF Injectable C II 1 mg/ml, 10 ml Ampule, 10/Pkg',
    imageUrl:
      'https://content.pattersonvet.com/LargeSquare/images/EPS_883637.jpg',
    isFavorite: false,
    manufacturer: 'RX GENERICS',
    manufacturerSku: '00641601910',
    description: '',
    offers: [
      {
        id: '9d7ed1e5-53c8-4fc3-bef1-d62d6f371196',
        vendor: {
          id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
          name: 'Patterson',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
          type: 'distributor' as const,
        },
        name: 'Duramorph PF Injectable C II 1 mg/ml, 10 ml Ampule, 10/Pkg',
        isPurchasable: true,
        vendorSku: '07-890-5486',
        price: null,
        clinicPrice: '337.57',
        stockStatus: 'IN_STOCK' as const,
        lastOrderedAt: null,
        lastOrderedQuantity: null,
        increments: 1,
        isRecommended: false,
        rebatePercent: null,
        product: null as unknown as ProductType, // Circular reference - will be set after product creation
        unitOfMeasure: null,
        size: null,
      },
      {
        id: '9d758983-8d89-4c3d-870d-951d799ed868',
        vendor: {
          id: '9d7559b3-0b71-4606-88b0-e903fc518846',
          name: 'Covetrus',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
          type: 'distributor' as const,
        },
        name: 'Duramorph PF Injectable C II 1 mg/ml, 10 ml Ampule, 10/Pkg',
        isPurchasable: true,
        vendorSku: '055013',
        price: null,
        clinicPrice: '303.72',
        stockStatus: 'BACKORDER' as const,
        lastOrderedAt: '2025-09-16T17:39:16.000000Z',
        lastOrderedQuantity: 1,
        increments: 1,
        isRecommended: false,
        rebatePercent: null,
        product: null as unknown as ProductType, // Circular reference - will be set after product creation
        unitOfMeasure: null,
        size: null,
      },
    ],
    attributes: [
      {
        name: 'NDC Number',
        value: '00641-6019-10',
      },
      {
        name: 'Volume',
        value: '10 ml',
      },
      {
        name: 'Strength',
        value: '1 mg/ml',
      },
      {
        name: 'Presentation',
        value: 'Liquid',
      },
      {
        name: 'Delivery Type',
        value: 'Injection',
      },
      {
        name: 'Container Type',
        value: 'Ampule',
      },
      {
        name: 'Package Quantity',
        value: '10/Pkg',
      },
      {
        name: 'Active Ingredient',
        value: 'Morphine',
      },
    ],
    isHazardous: false,
    requiresPrescription: true,
    requiresColdShipping: false,
    isControlledSubstance: true,
    isControlled222Form: true,
    requiresPedigree: false,
  },
  {
    id: '9ed26323-62fd-446b-9ea6-1dda246f361c',
    name: 'Pivetal WebMax Sutures u2013 Size 2, D195, 30" (CP), 12/Pkg Pivetalu00ae WebMaxu2122 Sutures u2013 Size 2, D195, 30" (CP), 12/Pkg',
    imageUrl:
      'https://content.pattersonvet.com/LargeSquare/images/JPG_1066858.jpg',
    isFavorite: false,
    manufacturer: 'PIVETAL',
    manufacturerSku: '21275273',
    description: '',
    offers: [
      {
        id: '9d7ed1db-667a-4fce-a118-ca5d93228694',
        vendor: {
          id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
          name: 'Patterson',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
          type: 'distributor' as const,
        },
        name: 'Pivetal WebMax Sutures – Size 2, D195, 30" (CP), 12/Pkg',
        isPurchasable: true,
        vendorSku: '07-891-0155',
        price: null,
        clinicPrice: '80.64',
        stockStatus: 'IN_STOCK' as const,
        lastOrderedAt: '2025-09-10T13:54:58.000000Z',
        lastOrderedQuantity: 1,
        increments: 1,
        isRecommended: true,
        rebatePercent: null,
        product: null as unknown as ProductType, // Circular reference - will be set after product creation
        unitOfMeasure: null,
        size: null,
      },
    ],
    attributes: [
      {
        name: 'Brand',
        value: 'Pivetalu00ae WebMaxu2122',
      },
      {
        name: 'Color',
        value: 'Violet',
      },
      {
        name: 'Braided',
        value: 'N',
      },
      {
        name: 'Sterile',
        value: 'Y',
      },
      {
        name: 'Material',
        value: 'Polydioxanone',
      },
      {
        name: 'Absorbable',
        value: 'Y',
      },
      {
        name: 'Needle Code',
        value: 'CP',
      },
      {
        name: 'Suture Size',
        value: '2',
      },
      {
        name: 'Autoclavable',
        value: 'N',
      },
      {
        name: 'Needle Point',
        value: 'Reverse Cutting',
      },
      {
        name: 'Needle Shape',
        value: 'Curved',
      },
      {
        name: 'Needle Length',
        value: '40 mm',
      },
      {
        name: 'Polyfilliment',
        value: 'N',
      },
      {
        name: 'Overall Length',
        value: '30"',
      },
      {
        name: 'MFG Suture Code',
        value: 'D195',
      },
      {
        name: 'Needle Included',
        value: 'Y',
      },
      {
        name: 'Package Quantity',
        value: '12/Pkg',
      },
      {
        name: 'Needle Circle Size',
        value: '1/2',
      },
    ],
    isHazardous: false,
    requiresPrescription: false,
    requiresColdShipping: false,
    isControlledSubstance: false,
    isControlled222Form: false,
    requiresPedigree: false,
  },
  {
    id: '9ed260a1-37cb-411c-a83b-afcafc41cbbb',
    name: 'VetraSeb Silver Antimicrobial Wipes, 84 Count',
    imageUrl:
      'https://ws.mwiah.com/media/image?id=044403bc-f1bd-4158-9934-77ccc6e11301',
    isFavorite: false,
    manufacturer: 'VetOne',
    manufacturerSku: '',
    description:
      'VetraSeb Silver Antimicrobial Wipes are pre-soaked with a broad spectrum formula containing colloidal nanoparticles of silver and indicated for use in dermatological skin conditions.nn* Contains 10 ppm colloidal nano particles of silvern* Silver helps support healthy skin and coatn* All natural, no stinging or discoloration to the skinn* For use on dogs, cats and horses',
    offers: [
      {
        id: '9d7584a7-224a-4685-85c8-923341720474',
        vendor: {
          id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
          name: 'Patterson',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
          type: 'distributor' as const,
        },
        name: 'VetraSeb Silver Antimicrobial Wipes, 84 Count',
        isPurchasable: true,
        vendorSku: '07-893-8980',
        price: '20.18',
        clinicPrice: '24.99',
        stockStatus: 'IN_STOCK' as const,
        lastOrderedAt: '2025-09-09T16:43:13.000000Z',
        lastOrderedQuantity: 4,
        increments: 1,
        isRecommended: false,
        rebatePercent: null,
        product: null as unknown as ProductType, // Circular reference - will be set after product creation
        unitOfMeasure: null,
        size: null,
      },
    ],
    attributes: [
      {
        name: 'Active Ingredient',
        value: 'colloidal nano particles of silver',
      },
      {
        name: 'Brand',
        value: 'VetraSeb',
      },
      {
        name: 'Form',
        value: 'wipe',
      },
      {
        name: 'Size',
        value: '84 ct',
      },
      {
        name: 'Species',
        value: 'canine, equine, feline',
      },
    ],
    isHazardous: false,
    requiresPrescription: false,
    requiresColdShipping: false,
    isControlledSubstance: false,
    isControlled222Form: false,
    requiresPedigree: false,
  },
];

// Set up circular references: each offer should reference its parent product
productsData.forEach((product) => {
  product.offers.forEach((offer) => {
    offer.product = product;
  });
});

const products = productsData;

type Props = {
  setActiveTab: (tab: number) => void;
};

export const CustomList = ({ setActiveTab }: Props) => {
  return (
    <CollapsiblePanel
      variant="white"
      header={
        <div className="flex h-[72px] w-full items-center justify-between bg-white p-5 pr-16 pb-0">
          <Checkbox checked={false} onChange={() => {}} />
          <div className="ml-4 flex w-full flex-col gap-1">
            <span className="text-sm font-semibold">My Shopping List</span>
            <div className="flex">
              <span className="text-xs text-black/50">
                Total of Items:{' '}
                <span className="font-semibold text-black/70">250</span>
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Created by:{' '}
                <span className="font-semibold text-black/70">Lima Neto</span>
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Last Updated:{' '}
                <span className="font-semibold text-black/70">04/05/2025</span>
              </span>
            </div>
          </div>
          {products && products.length > 0 ? (
            <Button className="w-40" size="sm" to={SHOP_ROUTES_PATH.checkout}>
              Checkout List
            </Button>
          ) : (
            <Button className="w-40" size="sm" onClick={() => setActiveTab(1)}>
              Add Products to List
            </Button>
          )}
        </div>
      }
      content={
        <div className="bg-white p-5">
          <div className="flex flex-col gap-1 bg-[#F8FBFD] p-4">
            {products && products.length > 0 ? (
              products.map((product) => (
                <Item key={product.id} product={product} />
              ))
            ) : (
              <EmptyState setActiveTab={setActiveTab} />
            )}
          </div>
          {!products ||
            (products.length === 0 && (
              <Button
                onClick={() => setActiveTab(1)}
                variant="unstyled"
                className="mt-4 w-full text-right text-blue-600"
              >
                <p className="mr-4 ml-auto">+ Add more products</p>
              </Button>
            ))}
        </div>
      }
      startOpen
    />
  );
};
