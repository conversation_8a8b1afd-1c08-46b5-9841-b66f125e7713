import { Checkbox } from '@/libs/form/Checkbox';
import { CustomList } from './components/CustomList/CustomList';

type Props = {
  setActiveTab: (tab: number) => void;
};

export const CustomLists = ({ setActiveTab }: Props) => {
  return (
    <div>
      <div className="mt-4 mb-6 flex">
        <Checkbox
          checked={false}
          onChange={() => {}}
          label="Select all your lists"
        />
      </div>
      <CustomList setActiveTab={setActiveTab} />
    </div>
  );
};
