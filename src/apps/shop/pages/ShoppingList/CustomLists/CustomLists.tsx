import { useState } from 'react';
import { Checkbox } from '@/libs/form/Checkbox';
import { Input } from '@/libs/form/Input';
import { CustomList } from './components/CustomList/CustomList';

type Props = {
  setActiveTab: (tab: number) => void;
};

export const CustomLists = ({ setActiveTab }: Props) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  return (
    <div>
      <div className="mt-4 mb-6 flex items-center justify-between">
        <Checkbox
          checked={false}
          onChange={() => {}}
          label="Select all your lists"
        />
        <form onSubmit={handleSearchSubmit} className="ml-auto">
          <Input
            placeholder="Search lists or products..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            size="sm"
            className="w-64"
          />
        </form>
      </div>
      <CustomList setActiveTab={setActiveTab} searchQuery={searchQuery} />
    </div>
  );
};
