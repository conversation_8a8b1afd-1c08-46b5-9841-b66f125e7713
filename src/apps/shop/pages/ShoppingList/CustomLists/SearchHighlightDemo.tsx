import React, { useState } from 'react';
import { Input } from '@/libs/form/Input';
import { Checkbox } from '@/libs/form/Checkbox';

// Demo component to showcase the search highlighting functionality
export const SearchHighlightDemo = () => {
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data for demonstration
  const mockProducts = [
    { id: '1', name: 'Simparica 6 x 5mg Gold 2.8 - 5.5lbs' },
    { id: '2', name: 'VetraSeb CeraDerm P Antiseptic Wipes' },
    { id: '3', name: 'Hill\'s Prescription Diet k/d Kidney Care' },
    { id: '4', name: 'Royal Canin Veterinary Diet' },
    { id: '5', name: 'Purina Pro Plan Veterinary Diets' },
  ];

  const listName = 'My Shopping List';

  // Sort products based on search query - matching products first
  const sortedProducts = React.useMemo(() => {
    if (!searchQuery.trim()) return mockProducts;

    const query = searchQuery.toLowerCase();
    const listNameMatches = listName.toLowerCase().includes(query);

    // If list name matches, show all products in original order
    if (listNameMatches) {
      return mockProducts;
    }

    // Otherwise, sort products with matches first
    const matchingProducts: typeof mockProducts = [];
    const nonMatchingProducts: typeof mockProducts = [];

    mockProducts.forEach((product) => {
      const productNameMatches = product.name.toLowerCase().includes(query);
      if (productNameMatches) {
        matchingProducts.push(product);
      } else {
        nonMatchingProducts.push(product);
      }
    });

    return [...matchingProducts, ...nonMatchingProducts];
  }, [mockProducts, searchQuery]);

  // Function to check if a product matches the search query
  const isProductMatch = (product: typeof mockProducts[0]): boolean => {
    if (!searchQuery.trim()) return false;
    const query = searchQuery.toLowerCase();
    const listNameMatches = listName.toLowerCase().includes(query);
    const productNameMatches = product.name.toLowerCase().includes(query);
    return !listNameMatches && productNameMatches;
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-xl font-semibold mb-4">Search Highlighting Demo</h2>

      {/* Search Header - matches the actual component layout */}
      <div className="mt-4 mb-6 flex items-center justify-between bg-gray-100/50 p-4 rounded">
        <Checkbox
          checked={false}
          onChange={() => {}}
          label="Select all your lists"
        />
        <form onSubmit={handleSearchSubmit} className="ml-auto">
          <Input
            placeholder="Search lists or products..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            size="sm"
            className="w-64"
          />
        </form>
      </div>

      {/* Search Results */}
      <div className="space-y-4">
        <div className="text-sm text-gray-600 mb-2">
          Search query: "{searchQuery}" | Showing {sortedProducts.length} product(s)
        </div>

        <div className="border border-gray-200 rounded-lg p-4 bg-white">
          <h3 className="font-semibold text-lg mb-2">
            {highlightText(listName, searchQuery)}
          </h3>
          <div className="text-sm text-gray-600 mb-3">
            Total of Items: <span className="font-semibold">{sortedProducts.length}</span>
          </div>
          <div className="space-y-2">
            {sortedProducts.map((product) => {
              const isHighlighted = isProductMatch(product);
              const containerClasses = isHighlighted
                ? "flex w-full justify-between rounded-lg border-2 border-blue-500 bg-gradient-to-r from-blue-50 to-blue-100 p-4 shadow-md"
                : "flex w-full justify-between rounded-lg border border-black/[0.06] bg-white p-4";

              return (
                <div key={product.id} className={containerClasses}>
                  <div className="flex flex-col justify-center gap-1">
                    <div className="text-sm font-semibold">
                      {highlightText(product.name, searchQuery)}
                    </div>
                    {isHighlighted && (
                      <div className="text-xs text-blue-600 font-medium">
                        ✨ Search Match - Moved to Top
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Demo Instructions */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-2">How it works:</h3>
        <ul className="text-sm space-y-1">
          <li>• <strong>No search:</strong> Products shown in original order</li>
          <li>• <strong>List name match:</strong> All products shown in original order</li>
          <li>• <strong>Product name match:</strong> Matching products moved to top with blue gradient border</li>
          <li>• <strong>Try searching:</strong> "simparica", "diet", "shopping", "wipes"</li>
        </ul>
      </div>
    </div>
  );
};

// Helper function to highlight search terms
const highlightText = (text: string, searchQuery: string) => {
  if (!searchQuery.trim()) return text;

  const regex = new RegExp(`(${searchQuery})`, 'gi');
  const parts = text.split(regex);

  return (
    <span>
      {parts.map((part, index) =>
        regex.test(part) ? (
          <mark key={index} className="bg-yellow-200">{part}</mark>
        ) : (
          part
        )
      )}
    </span>
  );
};
